import { Column, Entity, ManyToOne } from 'typeorm';
import { User } from './user.entity';
import { BaseEntity } from '../../config/repository/base-entity';

@Entity('swap_quotations')
export class SwapQuotation extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @Column({ name: 'from_currency' })
  fromCurrency: string;

  @Column({ name: 'to_currency' })
  toCurrency: string;

  @Column({ name: 'quoted_price' })
  quotedPrice: string;

  @Column({ name: 'quoted_currency' })
  quotedCurrency: string;

  @Column({ name: 'from_amount' })
  fromAmount: string;

  @Column({ name: 'to_amount' })
  toAmount: string;

  @Column({ default: false })
  confirmed: boolean;

  @Column({ name: 'expires_at' })
  expiresAt: string;
}

import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { User } from './user.entity';
import { Trade } from './trades.entity';

export enum OrderSide {
  BUY = 'buy',
  SELL = 'sell',
}

export enum OrderType {
  LIMIT = 'limit',
  MARKET = 'market',
}

export enum OrderStatus {
  PENDING = 'pending',
  DONE = 'done',
  CANCEL = 'cancel',
  WAIT = 'wait',
}

@Entity('orders')
export class Orders extends BaseEntity {
  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @OneToMany(() => Trade, (trade) => trade.order, { cascade: true })
  trades: Trade[];

  @Column({ unique: true })
  reference: string;

  @Column({ nullable: true, name: 'market_id' })
  marketId: string;

  @Column({ nullable: true, name: 'base_unit' })
  baseUnit: string;

  @Column({ nullable: true, name: 'quote_unit' })
  quoteUnit: string;

  @Column({ type: 'enum', enum: OrderSide })
  side: OrderSide;

  @Column({ type: 'enum', enum: OrderType, name: 'order_type' })
  orderType: OrderType;

  @Column({ name: 'price_unit' })
  priceUnit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, name: 'price_amount' })
  priceAmount: string;

  @Column({ name: 'avg_price_unit' })
  avgPriceUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
    name: 'avg_price_amount',
  })
  avgPriceAmount: string;

  @Column({ nullable: true, name: 'volume_unit' })
  volumeUnit: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, name: 'volume_amount' })
  volumeAmount: string;

  @Column({ nullable: true, name: 'origin_volume_unit' })
  originVolumeUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
    name: 'origin_volume_amount',
  })
  originVolumeAmount: string;

  @Column({ nullable: true, name: 'executed_volume_unit' })
  executedVolumeUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
    name: 'executed_volume_amount',
  })
  executedVolumeAmount: string;

  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.PENDING })
  status: OrderStatus;

  @Column({ nullable: true, name: 'trades_count' })
  tradesCount: string;

  @Column({ type: 'json', nullable: true })
  meta: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  errors: Record<string, any>;
}

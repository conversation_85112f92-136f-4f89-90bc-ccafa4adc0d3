import { Address } from './address.entity';
import { Orders } from './orders.entity';
import { Trade } from './trades.entity';
import { Transaction } from './transactions.entity';
import { SwapTransaction } from './swap-transaction.entity';
import { SwapQuotation } from './swap-quotation.entity';

describe('Entity Property Naming Tests', () => {
  describe('Address Entity', () => {
    it('should allow setting camelCase properties', () => {
      const address = new Address();

      // Test that camelCase properties can be set and retrieved
      address.destinationTag = 'test-tag';
      address.totalPayments = '100.50';

      expect(address.destinationTag).toBe('test-tag');
      expect(address.totalPayments).toBe('100.50');
    });
  });

  describe('Orders Entity', () => {
    it('should allow setting camelCase properties', () => {
      const order = new Orders();

      // Test that camelCase properties can be set and retrieved
      order.marketId = 'btc-usdt';
      order.baseUnit = 'BTC';
      order.quoteUnit = 'USDT';
      order.priceUnit = 'USDT';
      order.priceAmount = '50000.00';
      order.tradesCount = '5';

      expect(order.marketId).toBe('btc-usdt');
      expect(order.baseUnit).toBe('BTC');
      expect(order.quoteUnit).toBe('USDT');
      expect(order.priceUnit).toBe('USDT');
      expect(order.priceAmount).toBe('50000.00');
      expect(order.tradesCount).toBe('5');
    });
  });

  it('should successfully instantiate all entities with camelCase properties', () => {
    // Test that all entities can be instantiated and properties can be set
    const address = new Address();
    address.destinationTag = 'test-tag';
    expect(address.destinationTag).toBe('test-tag');

    const order = new Orders();
    order.marketId = 'btc-usdt';
    expect(order.marketId).toBe('btc-usdt');

    const trade = new Trade();
    trade.marketId = 'btc-usdt';
    expect(trade.marketId).toBe('btc-usdt');

    const transaction = new Transaction();
    transaction.transactionNote = 'test note';
    expect(transaction.transactionNote).toBe('test note');

    const swapTransaction = new SwapTransaction();
    swapTransaction.fromCurrency = 'BTC';
    expect(swapTransaction.fromCurrency).toBe('BTC');

    const swapQuotation = new SwapQuotation();
    swapQuotation.fromCurrency = 'BTC';
    expect(swapQuotation.fromCurrency).toBe('BTC');
  });
});

import { Entity, Column, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../config/repository/base-entity';
import { Orders } from './orders.entity';
import { User } from './user.entity';

@Entity('trades')
export class Trade extends BaseEntity {
  @ManyToOne(() => Orders, (order) => order.trades)
  order: Orders;

  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @Column({ name: 'market_id' })
  marketId: string;

  @Column({ name: 'market_base_unit' })
  marketBaseUnit: string;

  @Column({ name: 'market_quote_unit' })
  marketQuoteUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
    name: 'price_amount',
  })
  priceAmount: string;

  @Column({ name: 'price_unit' })
  priceUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
    name: 'volume_amount',
  })
  volumeAmount: string;

  @Column({ name: 'volume_unit' })
  volumeUnit: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 8,
    nullable: true,
    name: 'total_amount',
  })
  totalAmount: string;

  @Column({ name: 'total_unit' })
  totalUnit: string;
}

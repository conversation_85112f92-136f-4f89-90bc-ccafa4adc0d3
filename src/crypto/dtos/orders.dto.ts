import { ApiProperty } from '@nestjs/swagger';
import { OrderSide, OrderType, OrderStatus } from '../entities/orders.entity';
import {
  IsDateString,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
//todo ensure that all the strings dtos are numbers which would later be stringified
export class CreateOrderDto {
  @ApiProperty()
  @IsString()
  baseCurrency: string;

  @ApiProperty()
  @IsString()
  quoteCurrency: string;

  @ApiProperty()
  @IsString()
  side: 'buy' | 'sell';

  @ApiProperty()
  @IsString()
  orderType: 'limit' | 'market';

  @ApiProperty()
  @IsString()
  price?: string;

  @ApiProperty()
  @IsString()
  volume: string;
}

export class OrderDto {
  @IsString()
  id: string;

  @IsString()
  userId: string;

  @IsString()
  reference: string;

  @IsOptional()
  @IsString()
  marketId?: string;

  @IsOptional()
  @IsString()
  baseUnit?: string;

  @IsOptional()
  @IsString()
  quoteUnit?: string;

  @IsEnum(OrderSide)
  side: OrderSide;

  @IsEnum(OrderType)
  orderType: OrderType;

  @IsString()
  priceUnit: string;

  @IsString()
  priceAmount: string;

  @IsString()
  avgPriceUnit: string;

  @IsOptional()
  @IsString()
  avgPriceAmount?: string;

  @IsOptional()
  @IsString()
  volumeUnit?: string;

  @IsString()
  volumeAmount: string;

  @IsOptional()
  @IsString()
  originVolumeUnit?: string;

  @IsOptional()
  @IsString()
  originVolumeAmount?: string;

  @IsOptional()
  @IsString()
  executedVolumeUnit?: string;

  @IsOptional()
  @IsString()
  executedVolumeAmount?: string;

  @IsEnum(OrderStatus)
  status: OrderStatus;

  @IsOptional()
  @IsString()
  tradesCount?: string;

  @IsOptional()
  meta?: Record<string, any>;

  @IsOptional()
  errors?: Record<string, any>;
}

export class GetOrdersByUserIdDto {
  @ApiProperty()
  @IsDateString()
  startDate: string;

  @ApiProperty()
  @IsDateString()
  endDate: string;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;
}

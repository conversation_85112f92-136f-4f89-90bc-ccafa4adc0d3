import { Injectable, NotFoundException } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import {
  SwapTransaction,
  SwapTransactionStatus,
} from '../entities/swap-transaction.entity';
import { DataSource } from 'typeorm';
import { UserRepository } from './users.repository';
import { SwapQuotationRepository } from './swap-quotation.repository';

export interface CreateSwapTransactionDto {
  id: string;
  userId: string;
  quotationId?: string;
  from_currency: string;
  to_currency: string;
  from_amount: string;
  received_amount?: string;
  execution_price?: string;
  status?: SwapTransactionStatus;
}

@Injectable()
export class SwapTransactionRepository extends TypeOrmRepository<SwapTransaction> {
  constructor(
    dataSource: DataSource,
    private readonly userRepository: UserRepository,
    private readonly swapQuotationRepository: SwapQuotationRepository,
  ) {
    super(SwapTransaction, dataSource.createEntityManager());
  }

  async createSwapTransaction(
    transaction: CreateSwapTransactionDto,
  ): Promise<SwapTransaction> {
    const user = await this.userRepository.findOne({
      where: {
        userId: transaction.userId,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    let quotation = undefined;
    if (transaction.quotationId) {
      quotation = await this.swapQuotationRepository.getSwapQuotation(
        transaction.quotationId,
      );
    }

    const savedTransaction = await this.save({
      id: transaction.id,
      from_currency: transaction.from_currency,
      to_currency: transaction.to_currency,
      from_amount: transaction.from_amount,
      received_amount: transaction.received_amount,
      execution_price: transaction.execution_price,
      user,
      quotation,
    });

    return savedTransaction;
  }

  async getSwapTransaction(transactionId: string): Promise<SwapTransaction> {
    const transaction = await this.findOne({
      where: {
        id: transactionId,
      },
      relations: ['user', 'quotation'],
    });

    if (!transaction) {
      throw new NotFoundException(
        `Swap transaction with ID ${transactionId} not found`,
      );
    }

    return transaction;
  }

  async getSwapTransactionsByUserId(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ transactions: SwapTransaction[]; total: number }> {
    const query = this.createQueryBuilder('swap_transactions')
      .where('swap_transactions.userId = :userId', { userId })
      .andWhere('swap_transactions.createdAt >= :startDate', { startDate })
      .andWhere('swap_transactions.createdAt <= :endDate', { endDate })
      .orderBy('swap_transactions.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .leftJoinAndSelect('swap_transactions.user', 'user')
      .leftJoinAndSelect('swap_transactions.quotation', 'quotation');

    const [transactions, total] = await query.getManyAndCount();

    return { transactions, total };
  }

  async updateSwapTransactionStatus(
    transactionId: string,
    status: SwapTransactionStatus,
    receivedAmount?: string,
    executionPrice?: string,
  ): Promise<SwapTransaction> {
    const transaction = await this.getSwapTransaction(transactionId);

    transaction.status = status;
    if (receivedAmount) {
      transaction.receivedAmount = receivedAmount;
    }
    if (executionPrice) {
      transaction.executionPrice = executionPrice;
    }

    return await this.save(transaction);
  }
}
